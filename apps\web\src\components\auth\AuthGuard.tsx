'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * Professional Authentication Guard
 *
 * Simple, clean authentication check without loading screens.
 * Professional apps don't show "Checking authentication..." - they just work.
 */
export function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only redirect if we're certain there's no authentication
    // Add small delay to prevent race conditions with cookie setting
    if (!loading && !isAuthenticated && !user) {
      const timer = setTimeout(() => {
        router.push('/signin');
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [loading, isAuthenticated, user, router]);

  // If still loading, show nothing (prevents flash)
  if (loading) {
    return null;
  }

  // If not authenticated after loading, show nothing while redirecting
  if (!isAuthenticated || !user) {
    return null;
  }

  // User is authenticated, show the protected content
  return <>{children}</>;
}
