'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * Professional Authentication Guard
 *
 * Robust authentication check with proper state management.
 * Prevents redirect loops and handles edge cases gracefully.
 */
export function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading, isAuthenticated, error } = useAuth();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  useEffect(() => {
    // Mark initial load as complete after first render
    if (!initialLoadComplete && !loading) {
      setInitialLoadComplete(true);
    }
  }, [loading, initialLoadComplete]);

  useEffect(() => {
    // Only redirect if:
    // 1. Initial load is complete
    // 2. Not currently loading
    // 3. Not authenticated
    // 4. Haven't already redirected
    // 5. No authentication error that might resolve
    if (
      initialLoadComplete &&
      !loading &&
      !isAuthenticated &&
      !user &&
      !hasRedirected &&
      !error?.includes('Network error') // Don't redirect on network errors
    ) {
      console.log('🔒 AUTHGUARD: Redirecting to signin - no valid authentication');
      setHasRedirected(true);

      // Add delay to prevent race conditions with cookie setting
      const timer = setTimeout(() => {
        router.push('/signin');
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [initialLoadComplete, loading, isAuthenticated, user, hasRedirected, error, router]);

  // Show loading state only during initial load
  if (!initialLoadComplete || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated after loading, show nothing while redirecting
  if (!isAuthenticated || !user) {
    return null;
  }

  // User is authenticated, show the protected content
  return <>{children}</>;
}
