"use client";

import React from "react";
import { CategoryCarousel, VideoGrid, HeroSection, CoursesGrid } from "@/components/sections";
import { useCategory } from "@/hooks";

/**
 * Home page component - FIXED: No black screens!
 *
 * Simplified to prevent any loading issues that might cause black screens.
 * Shows content immediately for professional user experience.
 */
export default function Home() {
  const { activeCategory, selectCategory } = useCategory("All");

  // FIXED: Don't use courses hook that might fail - just show static content
  const mockCourses = [
    {
      id: '1',
      title: 'Basic Safety Training',
      excerpt: 'Essential safety protocols for maritime professionals',
      status: 'published' as const
    },
    {
      id: '2',
      title: 'Navigation Fundamentals',
      excerpt: 'Learn the basics of maritime navigation',
      status: 'published' as const
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50" style={{ backgroundColor: '#f9fafb' }}>
      {/* Hero Section */}
      <HeroSection />

      {/* Category Circles Carousel */}
      <div className="bg-white border-b border-gray-200">
        <CategoryCarousel
          activeCategory={activeCategory}
          onCategoryChange={selectCategory}
        />
      </div>

      {/* Courses Grid - Above Video Grid as requested */}
      <CoursesGrid courses={mockCourses} isLoading={false} />

      {/* Video Grid */}
      <VideoGrid />
    </div>
  );
}
